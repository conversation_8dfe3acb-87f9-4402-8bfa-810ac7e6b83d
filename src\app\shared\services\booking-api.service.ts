import { UpdateBookingRequest } from './../models/updateBookingRequest';
import { LoggerService } from './../interceptors/logger.service';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { AppConfig } from 'src/app/configs/app.config';
import { Observable } from 'rxjs';
import { BookingResponse } from '../models/booking-response.model';
import { BookingRequest } from '../models/booking.model';
import { AppConstants } from '../constants/AppConstants';
import { BookingDetails } from '../models/booking-details.model';
import { GenericAPIResponse } from '../models/generic-api-response.model';
import { UserBookingsResponse, UserBookingsRequest } from '../models/user-bookings.model';

@Injectable({
  providedIn: 'root'
})
export class BookingAPIService {

  private baseUrl: string;
  private logger: LoggerService;

  constructor(private httpClient: HttpClient) { 
    this.baseUrl = AppConfig.CabYaari_WebAPI_New;
    this.logger = LoggerService.createLogger('BookingAPIService');
  }

  // TODO Use genric instead of any
  getSelectedRouteCategoryFareDetails = (
    pickUpAddressLongLat: string,
    dropOffAddressLongLat: string,
    tripType: string,
    categoryName: string): Observable<GenericAPIResponse<BookingDetails>> => {
    this.logger.trace('getSelectedRouteCategoryFareDetails() called with pickUpAddressLongLat', pickUpAddressLongLat,
      'dropOffAddressLongLat', dropOffAddressLongLat,
      'tripType', tripType,
      'categoryName', categoryName);

      const encodedCategoryName = encodeURIComponent(categoryName); // encodes + to %2B, space to %20, etc.
      const encodedTripType = encodeURIComponent(tripType);
      const encodedPickup = encodeURIComponent(pickUpAddressLongLat);
      const encodedDropoff = encodeURIComponent(dropOffAddressLongLat);
    
      const query = `?${AppConstants.BOOKING_PICKUP_LNG_LAT_QUERY_PARAM}=${encodedPickup}` +
                    `&${AppConstants.BOOKING_DROPOFF_LNG_LAT_QUERY_PARAM}=${encodedDropoff}` +
                    `&${AppConstants.BOOKING_TRIP_TYPE_QUERY_PARAM}=${encodedTripType}` +
                    `&${AppConstants.BOOKING_CATEGORY_QUERY_PARAM}=${encodedCategoryName}`;
    
      const url = `${this.baseUrl}/api/v1/Booking/GetSelectedtRouteCategoryFareDetails${query}`;
    
      return this.httpClient.get<GenericAPIResponse<BookingDetails>>(url);
  }

  updateBooking = (request): Observable<BookingResponse> => {
    this.logger.trace('updateBooking() called with request', request);
    const url = this.baseUrl + '/api/v1/Booking/PaymentStatusUpdate';
    this.logger.trace('url', url);
    const httpOptions = {
      headers: new HttpHeaders(
        {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ` + localStorage.getItem('access_token'),
        }
      )
    };
    return this.httpClient.post<BookingResponse>(url, JSON.stringify(request), httpOptions);
  }


  createNewBooking = (request: BookingRequest): Observable<BookingResponse> => {
    this.logger.trace('createNewBooking() called with request', JSON.stringify(request));
    const url = this.baseUrl + '/api/v1/Booking/NewBooking';
    this.logger.trace('url', url);
    const httpOptions = {
      headers: new HttpHeaders(
        {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ` + localStorage.getItem('access_token'),
        }
      )
    };
    return this.httpClient.post<BookingResponse>(url, JSON.stringify(request), httpOptions);
  }

  getBookingById = (bookingId: string): Observable<any> => {
    this.logger.trace('getBookingById() called with bookingId', bookingId);
    const url = `${this.baseUrl}/api/v1/booking/${bookingId}`;
    this.logger.trace('url', url);
    const httpOptions = {
      headers: new HttpHeaders(
        {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ` + localStorage.getItem('access_token'),
        }
      )
    };
    return this.httpClient.get<any>(url, httpOptions);
  }

  getUserBookings = (request: UserBookingsRequest = {}): Observable<UserBookingsResponse> => {
    this.logger.trace('getUserBookings() called with request', request);

    // Set default page size if not provided
    const pageSize = request.pageSize || 10;

    // Build query parameters
    let params = new HttpParams().set('pageSize', pageSize.toString());

    if (request.cursor) {
      params = params.set('cursor', request.cursor);
    }

    const url = `${this.baseUrl}/api/v1/booking/user-bookings`;
    this.logger.trace('url', url);
    this.logger.trace('params', params.toString());

    // Note: Using HttpClient with JWT interceptor for automatic auth header injection
    return this.httpClient.get<UserBookingsResponse>(url, { params });
  }
}
